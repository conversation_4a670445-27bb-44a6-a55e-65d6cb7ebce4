from google.cloud import run_v2
from google.cloud.run_v2 import RunJobRequest, EnvVar
import config
import logging


logger = logging.getLogger(__name__)

async def launch_data_processing_job_gcp(user_id:str, record_ids:list, bucket_name:str, parser:dict, batch_id:str|None = None):
    """
    Runs an existing Cloud Run Job for sequential report generation with an overridden environment variable.
    Args:
        user_id: The USER_ID value to override.
        record_ids: List of record IDs to process.
        bucket_name: The name to the users bucket.
        parser: dict with parser job details
        batch_id: The ID of the batch to process.
    """

    job_parent = f"projects/{config.PROJECT_ID}/locations/{parser['region']}"
    job_full_name = f"{job_parent}/jobs/{parser['job_name']}"

    overrides = RunJobRequest.Overrides(
        task_count=len(record_ids),
        container_overrides=[
            RunJobRequest.Overrides.ContainerOverride(
                env=[
                    EnvVar(name="OPENAI_API_KEY", value=config.OPENAI_API_KEY),
                    EnvVar(name="OPENAI_BASE_URL", value=config.OPENAI_BASE_URL),
                    EnvVar(name="DB_USERNAME", value=config.DB_USERNAME),
                    EnvVar(name="DB_PASSWORD", value=config.DB_PASSWORD),
                    EnvVar(name="DB_HOST", value=config.DB_HOST),
                    EnvVar(name="DATABASE_NAME", value=config.DATABASE_NAME),
                    EnvVar(name="BIOMARKERS_TABLE_NAME", value=config.BIOMARKERS_TABLE_NAME),
                    EnvVar(name="RECORDS_TABLE_NAME", value=config.RECORDS_TABLE_NAME),
                    EnvVar(name="FILE_NAME_COLUMN", value=config.FILE_NAME_COLUMN),
                    EnvVar(name="USER_ID", value=user_id),
                    EnvVar(name="MODEL_NAME", value=config.MODEL),
                    EnvVar(name="BUCKET_NAME", value=bucket_name),
                    EnvVar(name="GCP_SERVICE_JSON", value=config.GCP_SERVICE_JSON),
                    EnvVar(name="PROJECT_ID", value=config.PROJECT_ID),
                    EnvVar(name="N1_API_HEADER", value=config.N1_API_HEADER_NAME),
                    EnvVar(name="N1_API_KEY", value=config.N1_API_KEY),
                    EnvVar(name="N1_API_RECORD_STATUS_URL", value=config.N1_API_RECORD_STATUS_URL),
                    EnvVar(name="N1_API_SYNC_BIOMARKERS_URL", value=config.N1_API_SYNC_BIOMARKERS_URL),
                    EnvVar(name="N1_API_BASE_URL", value=config.N1_API_BASE_URL),
                    EnvVar(name="BATCH_ID", value=batch_id or ""),
                ],
                args=record_ids
            ),
        ]
    )
    try:
        async with run_v2.JobsAsyncClient() as client:
            request = RunJobRequest(
                name=job_full_name,
                overrides=overrides,
            )

            operation = await client.run_job(request=request)
            operation_id = operation.operation.name.split('/')[-1]
            logger.info(f"Launched job {job_full_name} with operation ID {operation_id}")
            logger.info(f"Request variables: USER_ID={user_id}, BUCKET_NAME={bucket_name}, record_ids={record_ids}")
            return operation_id

    except Exception as e:
        raise RuntimeError(f"Failed to launch job: {str(e)}")
