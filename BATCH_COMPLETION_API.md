# Batch Completion API Endpoint

## Overview

The new `/records/complete/batches` endpoint allows you to mark multiple batches of records as completed in a single API call. This endpoint updates all records within the specified batches to have:

- `status = "COMPLETED"`
- `message = "Enrichment completed"`
- `progress = 100`
- `updated_at = current timestamp`

## Endpoint Details

**URL:** `POST /records/complete/batches`

**Authentication:** Requires API Key authentication

**Content-Type:** `application/json`

## Request Schema

```json
{
    "user_id": "uuid-of-user",
    "batch_ids": ["batch_id_1", "batch_id_2", "batch_id_3"]
}
```

### Request Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `user_id` | UUID | Yes | The UUID of the user who owns the batches |
| `batch_ids` | Array[String] | Yes | List of batch identifiers to complete |

## Response Schema

```json
{
    "status": "success",
    "user_id": "uuid-of-user",
    "completed_batches": ["batch_id_1", "batch_id_2"],
    "failed_batches": [
        {
            "batch_id": "batch_id_3",
            "error": "No records found for batch batch_id_3"
        }
    ],
    "total_records_updated": 15,
    "updated_at": "2025-07-23T17:30:00.000Z"
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `status` | String | Overall operation status: "success", "partial_success", or "failed" |
| `user_id` | UUID | The UUID of the user |
| `completed_batches` | Array[String] | List of successfully completed batch IDs |
| `failed_batches` | Array[Object] | List of failed batches with error messages |
| `total_records_updated` | Integer | Total number of records updated across all batches |
| `updated_at` | DateTime | Timestamp when the operation was performed |

## Status Values

- **"success"**: All batches were completed successfully
- **"partial_success"**: Some batches were completed, others failed
- **"failed"**: No batches were completed successfully

## Example Usage

### cURL Example

```bash
curl -X POST "https://your-api-domain.com/records/complete/batches" \
  -H "Content-Type: application/json" \
  -H "N1-Api-Key: your-api-key" \
  -d '{
    "user_id": "123e4567-e89b-12d3-a456-************",
    "batch_ids": ["batch-001", "batch-002", "batch-003"]
  }'
```

### Python Example

```python
import requests
import json

url = "https://your-api-domain.com/records/complete/batches"
headers = {
    "Content-Type": "application/json",
    "N1-Api-Key": "your-api-key"
}

payload = {
    "user_id": "123e4567-e89b-12d3-a456-************",
    "batch_ids": ["batch-001", "batch-002", "batch-003"]
}

response = requests.post(url, headers=headers, data=json.dumps(payload))

if response.status_code == 200:
    result = response.json()
    print(f"Status: {result['status']}")
    print(f"Completed batches: {result['completed_batches']}")
    print(f"Total records updated: {result['total_records_updated']}")
else:
    print(f"Error: {response.status_code} - {response.text}")
```

### JavaScript Example

```javascript
const completeBatches = async (userId, batchIds) => {
  const response = await fetch('/records/complete/batches', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'N1-Api-Key': 'your-api-key'
    },
    body: JSON.stringify({
      user_id: userId,
      batch_ids: batchIds
    })
  });

  if (response.ok) {
    const result = await response.json();
    console.log('Batch completion result:', result);
    return result;
  } else {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
};

// Usage
completeBatches(
  '123e4567-e89b-12d3-a456-************',
  ['batch-001', 'batch-002', 'batch-003']
).then(result => {
  console.log(`${result.total_records_updated} records updated`);
}).catch(error => {
  console.error('Error completing batches:', error);
});
```

## Error Handling

### HTTP Status Codes

- **200 OK**: Request processed successfully (check response status for operation details)
- **422 Unprocessable Entity**: Invalid request payload (missing required fields, invalid UUID format, etc.)
- **500 Internal Server Error**: Server error during processing

### Common Error Scenarios

1. **Invalid User ID**: If the user doesn't exist, the operation will fail
2. **Non-existent Batches**: Batches that don't exist will be listed in `failed_batches`
3. **Empty Batch List**: Providing an empty `batch_ids` array will return success with 0 records updated
4. **Database Errors**: Connection or query issues will result in a 500 error

## Integration Notes

- This endpoint is synchronous and will process all batches before returning
- For large numbers of batches or records, consider implementing client-side timeouts
- The endpoint respects existing user permissions and will only update records belonging to the specified user
- All database operations are transactional per batch - if a batch fails, other batches are not affected
- The endpoint follows the same authentication patterns as other N1 API endpoints

## Testing

The endpoint includes comprehensive test coverage:

- **Integration Tests**: Test the full HTTP endpoint with various scenarios
- **Unit Tests**: Test the service layer logic with database interactions
- **Error Handling**: Test various failure scenarios and edge cases

Tests can be run with:
```bash
uv run pytest tests/integration/test_complete_batches.py tests/unit/test_complete_batches_service.py -v
```
