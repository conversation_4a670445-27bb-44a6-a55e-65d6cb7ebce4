import pytest
import uuid
from datetime import datetime, timezone
from unittest.mock import MagicMock, AsyncMock, patch
from fastapi import status

@pytest.mark.integration
class TestCompleteBatchesEndpoint:

    @pytest.fixture
    def mock_user(self, db_session):
        """Create a mock user in the database."""
        from models import UserORM
        user_id = uuid.uuid4()
        user = UserORM(
            id=user_id,
            bubble_id="12345678",
            email="<EMAIL>",
            develop_mode=False,
            bucket_name="test-bucket"
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        return user

    @pytest.fixture
    def mock_batch_ids(self):
        """Create mock batch IDs."""
        return [f"batch-{str(uuid.uuid4())[:8]}", f"batch-{str(uuid.uuid4())[:8]}"]

    @pytest.mark.asyncio
    async def test_complete_batches_success(self, auth_client, mock_user, mock_batch_ids, mock_n1_process_pipeline, monkeypatch):
        """Test completing multiple batches successfully."""
        # Mock the complete_batches method
        now = datetime.now(timezone.utc)
        mock_response = {
            "status": "success",
            "user_id": str(mock_user.id),
            "completed_batches": mock_batch_ids,
            "failed_batches": [],
            "total_records_updated": 5,
            "updated_at": now
        }
        mock_complete_batches = AsyncMock(return_value=mock_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "complete_batches", mock_complete_batches)

        # Prepare request payload
        request_payload = {
            "user_id": str(mock_user.id),
            "batch_ids": mock_batch_ids
        }

        # Make the request
        response = auth_client.post(
            "/records/complete/batches",
            json=request_payload
        )

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "success"
        assert data["user_id"] == str(mock_user.id)
        assert data["completed_batches"] == mock_batch_ids
        assert data["failed_batches"] == []
        assert data["total_records_updated"] == 5
        assert "updated_at" in data

        # Verify the mocked method was called with correct arguments
        mock_complete_batches.assert_called_once()

    @pytest.mark.asyncio
    async def test_complete_batches_partial_success(self, auth_client, mock_user, mock_batch_ids, mock_n1_process_pipeline, monkeypatch):
        """Test completing batches with partial success."""
        # Mock the complete_batches method with partial success
        now = datetime.now(timezone.utc)
        mock_response = {
            "status": "partial_success",
            "user_id": str(mock_user.id),
            "completed_batches": [mock_batch_ids[0]],
            "failed_batches": [{"batch_id": mock_batch_ids[1], "error": "No records found for batch"}],
            "total_records_updated": 3,
            "updated_at": now
        }
        mock_complete_batches = AsyncMock(return_value=mock_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "complete_batches", mock_complete_batches)

        # Prepare request payload
        request_payload = {
            "user_id": str(mock_user.id),
            "batch_ids": mock_batch_ids
        }

        # Make the request
        response = auth_client.post(
            "/records/complete/batches",
            json=request_payload
        )

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "partial_success"
        assert data["user_id"] == str(mock_user.id)
        assert len(data["completed_batches"]) == 1
        assert len(data["failed_batches"]) == 1
        assert data["total_records_updated"] == 3

    @pytest.mark.asyncio
    async def test_complete_batches_error(self, auth_client, mock_user, mock_batch_ids, mock_n1_process_pipeline, monkeypatch):
        """Test completing batches with an error."""
        # Mock the complete_batches method to raise an exception
        mock_complete_batches = AsyncMock(side_effect=Exception("Test error"))
        monkeypatch.setattr(mock_n1_process_pipeline, "complete_batches", mock_complete_batches)

        # Prepare request payload
        request_payload = {
            "user_id": str(mock_user.id),
            "batch_ids": mock_batch_ids
        }

        # Make the request
        response = auth_client.post(
            "/records/complete/batches",
            json=request_payload
        )

        # Verify the response
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        data = response.json()
        assert "detail" in data
        assert "Error completing batches" in data["detail"]

    @pytest.mark.asyncio
    async def test_complete_batches_invalid_payload(self, auth_client, mock_user):
        """Test completing batches with invalid payload."""
        # Prepare invalid request payload (missing batch_ids)
        request_payload = {
            "user_id": str(mock_user.id)
            # Missing batch_ids
        }

        # Make the request
        response = auth_client.post(
            "/records/complete/batches",
            json=request_payload
        )

        # Verify the response
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @pytest.mark.asyncio
    async def test_complete_batches_empty_batch_list(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test completing batches with empty batch list."""
        # Mock the complete_batches method
        now = datetime.now(timezone.utc)
        mock_response = {
            "status": "success",
            "user_id": str(mock_user.id),
            "completed_batches": [],
            "failed_batches": [],
            "total_records_updated": 0,
            "updated_at": now
        }
        mock_complete_batches = AsyncMock(return_value=mock_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "complete_batches", mock_complete_batches)

        # Prepare request payload with empty batch list
        request_payload = {
            "user_id": str(mock_user.id),
            "batch_ids": []
        }

        # Make the request
        response = auth_client.post(
            "/records/complete/batches",
            json=request_payload
        )

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "success"
        assert data["completed_batches"] == []
        assert data["total_records_updated"] == 0
