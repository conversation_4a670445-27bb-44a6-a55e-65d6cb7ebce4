import pytest
from fastapi import status, HTTPException
from unittest.mock import <PERSON>Mock, AsyncMock
import io
import uuid
from datetime import datetime, timezone

from models import UserORM, UserRecordRequestORM, UserBiomarkerORM
# Import necessary schemas
from schemas import N1RecordUpdateRequest,UserBiomarkerBase, BiomarkerResponse, PaginatedRecordResponse, UserBiomarkerDeleted


@pytest.mark.integration
class TestRecordsRoutes:

    @pytest.fixture
    def mock_user(self, db_session):
        """Create a mock user in the database."""
        user_id = uuid.uuid4()
        user = UserORM(
            id=user_id,
            bubble_id="12345678",
            email="<EMAIL>",
            develop_mode=False,
            bucket_name="test-bucket"
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        return user

    @pytest.fixture
    def mock_record(self, db_session, mock_user):
        """Create a mock record in the database."""
        record_id = str(uuid.uuid4())
        record = UserRecordRequestORM(
            id=record_id,
            user_id=mock_user.id,
            url="https://example.com/test.pdf",
            file_name="test.pdf",
            type="RECORD",
            status="COMPLETED",
            progress=100,
            batch_id="batch123"
        )
        db_session.add(record)
        db_session.commit()
        db_session.refresh(record)
        return record

    @pytest.fixture
    def mock_user_biomarker(self, db_session, mock_user, mock_record):
        """Create mock user biomarker in the database."""
        biomarker_id = uuid.uuid4()
        user_biomarker = UserBiomarkerORM(
            id=biomarker_id,
            record_id=mock_record.id,
            user_id=mock_user.id,
            test_name="Glucose",
            expanded_test_name=None,
            result="120",
            reference_range="70-99",
            unit="mg/dL",
            context="Diabetes",
            canonical_id=None,
            created_at=None,
            updated_at=None,
            edited=False,
            excluded=False,
            test_date=None,
            sample_source="Blood",
            method="Laboratory",
            additional_data=None,
            page_number=1,
            result_numeric=120.0,
            reference_range_min=70.0,
            reference_range_max=99.0,
            converted_result_numeric=100.0,
            converted_reference_range_min=60.0,
            converted_reference_range_max=88.0,
            out_of_range=True
        )
        db_session.add(user_biomarker)
        db_session.commit()
        db_session.refresh(user_biomarker)
        return user_biomarker

    @pytest.mark.asyncio
    # Add monkeypatch parameter
    async def test_get_record_status(self, auth_client, mock_user, mock_record, mock_n1_process_pipeline, monkeypatch):
        """Test getting record status."""
        # Mock the get_record_status method to return an object matching the response model
        # The route returns N1RecordUpdateRequest, so the mock should too.
        mock_response = N1RecordUpdateRequest(
            id=mock_record.id,
            user_id=mock_user.id,
            status=mock_record.status,
            progress=mock_record.progress,
            url="https://mock.storage.url/mock-file?signed=true", # Use the URL from get_file_url mock
            file_name=mock_record.file_name,
            type=mock_record.type,
            created_at=mock_record.created_at, # Ensure datetime objects are included
            updated_at=mock_record.updated_at
        )
        # Use monkeypatch to replace the method on the real instance
        monkeypatch.setattr(mock_n1_process_pipeline, "get_record_status", MagicMock(return_value=mock_response))
        # Also mock the get_file_url call made within the route handler
        monkeypatch.setattr(mock_n1_process_pipeline, "get_file_url", MagicMock(return_value="https://mock.storage.url/mock-file?signed=true"))

        # Original path without trailing slash
        response = auth_client.get(f"/records/status?user_id={mock_user.id}&record_id={mock_record.id}")
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == mock_response.id
        assert data["user_id"] == str(mock_response.user_id)
        assert data["status"] == mock_response.status
        assert data["url"] == mock_response.url # Check URL from the response model instance

    @pytest.mark.asyncio
    async def test_get_record_status_not_found(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch): # Add monkeypatch
        """Test getting record status for a non-existent record."""
        # Mock the get_record_status method to raise the expected HTTPException
        # The route handler catches the exception and returns 404
        mock_get_status = MagicMock(side_effect=HTTPException(status_code=404, detail="Record not found"))
        # Add comma
        monkeypatch.setattr(mock_n1_process_pipeline, "get_record_status", mock_get_status)

        non_existent_id = str(uuid.uuid4())
        response = auth_client.get(f"/records/status?user_id={mock_user.id}&record_id={non_existent_id}")

        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "detail" in data
        assert "Record not found" in data["detail"]

    @pytest.mark.asyncio
    async def test_get_user_records(self, auth_client, mock_user, mock_record, mock_n1_process_pipeline, monkeypatch): # Add monkeypatch
        """Test getting all records for a user."""
        # Mock the get_user_records method to return a list of objects matching the response model
        mock_response_item = N1RecordUpdateRequest(
            id=mock_record.id,
            user_id=mock_user.id,
            status=mock_record.status,
            progress=mock_record.progress,
            url=mock_record.url, # Assuming URL is part of the ORM object here
            file_name=mock_record.file_name,
            type=mock_record.type,
            created_at=mock_record.created_at,
            updated_at=mock_record.updated_at
        )
        # Use monkeypatch to replace the async method with an AsyncMock
        mock_get_records = AsyncMock(return_value=[mock_response_item])
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_records", mock_get_records)

        response = auth_client.get(f"/records/user/{mock_user.id}")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 1
        assert data[0]["id"] == mock_response_item.id
        assert data[0]["user_id"] == str(mock_response_item.user_id)
        assert data[0]["status"] == mock_response_item.status

    @pytest.mark.asyncio
    async def test_upload_record(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test uploading a record."""
        # Mock the background task execution
        mock_background_tasks = MagicMock()
        monkeypatch.setattr("fastapi.BackgroundTasks", lambda: mock_background_tasks)

        # Create test file
        test_file = io.BytesIO(b"test file content")
        test_file.name = "test.pdf"

        record_id = str(uuid.uuid4())
        batch_id = "batch123"

        # Prepare form data
        form_data = {
            "user_id": str(mock_user.id),
            "record_id": record_id,
            "batch_id": batch_id
        }

        files = {"file": ("test.pdf", test_file, "application/pdf")}

        # Mock the pipeline's update_user_records method using monkeypatch
        mock_update_records = AsyncMock()
        monkeypatch.setattr(mock_n1_process_pipeline, "update_user_records", mock_update_records)

        response = auth_client.post(
            "/records/add",
            data=form_data,
            files=files
        )

        # The response model is N1RecordUpdateRequest, check relevant fields
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "PENDING" # Corrected status
        # Don't check the exact ID as it might be generated by the server
        assert "id" in data and data["id"]  # Just verify ID exists and is not empty
        assert data["user_id"] == str(mock_user.id)
        # Verify background task was added (the route handler adds the task)
        # The actual call happens within update_user_records, so we check if the mock method was called
        assert mock_n1_process_pipeline.update_user_records.called
        assert mock_update_records.called

        # Check that the first argument (user_id) and third argument (batch_id) match what we expect
        args, _ = mock_update_records.call_args
        assert args[0] == mock_user.id
        assert args[2] == batch_id  # Third arg should be batch_id

    @pytest.mark.asyncio
    async def test_delete_record(self, auth_client, mock_user, mock_record, mock_n1_process_pipeline, monkeypatch): # Add monkeypatch
        """Test deleting a record."""
        # Mock the pipeline's delete_record method using monkeypatch
        mock_return = {"message": f"Record {mock_record.id} deleted successfully"}
        mock_delete = MagicMock(return_value=mock_return)
        monkeypatch.setattr(mock_n1_process_pipeline, "delete_record", mock_delete)

        response = auth_client.delete(f"/records/{mock_user.id}/{mock_record.id}")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data == mock_return

        # Verify the mocked method was called
        assert mock_delete.called
        args, _ = mock_delete.call_args
        assert args[0] == mock_user.id
        assert args[1] == mock_record.id  # Second arg should be record_id

    @pytest.mark.asyncio
    async def test_update_record_progress(self, auth_client, mock_user, mock_record, mock_n1_process_pipeline, monkeypatch): # Add monkeypatch
        """Test updating record progress."""
        update_data = {
            "user_id": str(mock_user.id),
            "record_id": mock_record.id,
            "progress": 50,
            "status": "IN_PROGRESS",
            "message": "Processing record"
        }

        # Mock the pipeline's update_record_progress method
        updated_record_mock = MagicMock(spec=UserRecordRequestORM)
        updated_record_mock.id = mock_record.id
        updated_record_mock.user_id = str(mock_user.id)
        updated_record_mock.progress = 50
        updated_record_mock.status = "IN_PROGRESS"
        # Add other necessary fields if the response schema requires them
        updated_record_mock.url = mock_record.url
        updated_record_mock.file_name = mock_record.file_name
        updated_record_mock.type = mock_record.type
        updated_record_mock.batch_id = mock_record.batch_id
        updated_record_mock.created_at = mock_record.created_at # Keep original created_at
        updated_record_mock.updated_at = datetime.now() # Should be updated

        # Create the expected response object based on the mock data
        mock_response = N1RecordUpdateRequest(
            id=updated_record_mock.id,
            user_id=updated_record_mock.user_id,
            status=updated_record_mock.status,
            progress=updated_record_mock.progress,
            url=updated_record_mock.url,
            file_name=updated_record_mock.file_name,
            type=updated_record_mock.type,
            created_at=updated_record_mock.created_at,
            updated_at=updated_record_mock.updated_at
        )
        # Use monkeypatch to replace the async method with an AsyncMock
        mock_update_progress = AsyncMock(return_value=mock_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "update_record_progress", mock_update_progress)

        response = auth_client.post("/records/status", json=update_data)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == mock_response.id
        assert data["progress"] == mock_response.progress
        assert data["status"] == mock_response.status

        # Verify the pipeline method was called with the correct Pydantic model
        # FastAPI converts the JSON payload into RecordProgressUpdateRequest
        from schemas import RecordProgressUpdateRequest
        expected_request_obj = RecordProgressUpdateRequest(**update_data)
        mock_update_progress.assert_called_once_with(expected_request_obj)

    @pytest.mark.asyncio
    async def test_get_user_biomarkers(self, auth_client, mock_user, mock_record, mock_user_biomarker, mock_n1_process_pipeline, monkeypatch):
        """Test getting biomarkers for a user."""
        # Mock the get_user_biomarkers method
        mock_data_item = UserBiomarkerBase(
            record_id=mock_record.id,
            user_id=mock_user.id,
            test_name=mock_user_biomarker.test_name,
            expanded_test_name=mock_user_biomarker.expanded_test_name,
            result=mock_user_biomarker.result,
            reference_range=mock_user_biomarker.reference_range,
            unit=mock_user_biomarker.unit,
            context=mock_user_biomarker.context,
            canonical_id=mock_user_biomarker.canonical_id,
            edited=mock_user_biomarker.edited,
            excluded=mock_user_biomarker.excluded,
            test_date=mock_user_biomarker.test_date,
            sample_source=mock_user_biomarker.sample_source,
            method=mock_user_biomarker.method,
            additional_data=mock_user_biomarker.additional_data,
            page_number=mock_user_biomarker.page_number,
            result_numeric=mock_user_biomarker.result_numeric,
            reference_range_min=mock_user_biomarker.reference_range_min,
            reference_range_max=mock_user_biomarker.reference_range_max,
            converted_result_numeric=mock_user_biomarker.converted_result_numeric,
            converted_reference_range_max=mock_user_biomarker.converted_reference_range_max,
            converted_reference_range_min=mock_user_biomarker.converted_reference_range_min,
            out_of_range=mock_user_biomarker.out_of_range,
            canonical_name=getattr(mock_user_biomarker, 'canonical_name', None),
        )
        biomarker_response = {
            "status": "success",
            "user_id": str(mock_user.id),
            "fields": list(UserBiomarkerBase.model_fields.keys()),
            "data": [mock_data_item]
        }
        mock_response_obj = BiomarkerResponse(**biomarker_response)
        mock_get_biomarkers = AsyncMock(return_value=mock_response_obj)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_biomarkers_response", mock_get_biomarkers)

        response = auth_client.get(f"/records/user-biomarkers?user_id={mock_user.id}")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        print("test_get_user_biomarkers response:", data)
        assert data["status"] == mock_response_obj.status
        assert data["user_id"] == str(mock_response_obj.user_id)
        assert isinstance(data["data"], list)
        assert len(data["data"]) == 1
        response_item = data["data"][0]
        # assert "id" in response_item
        assert response_item["test_name"] == mock_response_obj.data[0].test_name
        assert response_item["result"] == mock_response_obj.data[0].result
        # assert response_item["id"] == str(mock_response_obj.data[0].id)


    @pytest.mark.asyncio
    async def test_get_record_biomarkers(self, auth_client, mock_user, mock_record, mock_user_biomarker, mock_n1_process_pipeline, monkeypatch):
        """Test getting biomarkers for a specific record."""
        mock_data_item = UserBiomarkerBase(
            id=mock_user_biomarker.id,
            record_id=mock_record.id,
            user_id=mock_user.id,
            test_name=mock_user_biomarker.test_name,
            expanded_test_name=mock_user_biomarker.expanded_test_name,
            result=mock_user_biomarker.result,
            reference_range=mock_user_biomarker.reference_range,
            unit=mock_user_biomarker.unit,
            context=mock_user_biomarker.context,
            canonical_id=mock_user_biomarker.canonical_id,
            edited=mock_user_biomarker.edited,
            excluded=mock_user_biomarker.excluded,
            test_date=mock_user_biomarker.test_date,
            sample_source=mock_user_biomarker.sample_source,
            method=mock_user_biomarker.method,
            additional_data=mock_user_biomarker.additional_data,
            page_number=mock_user_biomarker.page_number,
            result_numeric=mock_user_biomarker.result_numeric,
            reference_range_min=mock_user_biomarker.reference_range_min,
            reference_range_max=mock_user_biomarker.reference_range_max,
            converted_result_numeric=mock_user_biomarker.converted_result_numeric,
            converted_reference_range_max=mock_user_biomarker.converted_reference_range_max,
            converted_reference_range_min=mock_user_biomarker.converted_reference_range_min,
            out_of_range=mock_user_biomarker.out_of_range,
            # canonical_name=mock_user_biomarker.canonical_name,
            created_at=mock_user_biomarker.created_at,
            updated_at=mock_user_biomarker.updated_at,
        )
        biomarker_response = {
            "status": "success",
            "user_id": str(mock_user.id),
            "fields": list(UserBiomarkerBase.model_fields.keys()),
            "data": [mock_data_item]
        }
        mock_response_obj = BiomarkerResponse(**biomarker_response)
        mock_get_biomarkers = AsyncMock(return_value=mock_response_obj)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_biomarkers_response", mock_get_biomarkers)

        response = auth_client.get(f"/records/user-biomarkers?user_id={mock_user.id}&record_id={mock_record.id}")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        print("test_get_record_biomarkers response:", data)
        assert data["status"] == mock_response_obj.status
        assert data["user_id"] == str(mock_response_obj.user_id)
        assert isinstance(data["data"], list)
        assert len(data["data"]) == 1
        response_item = data["data"][0]
        # assert "record_id" in response_item
        # assert response_item["record_id"] == mock_response_obj.data[0].record_id
        assert response_item["test_name"] == mock_response_obj.data[0].test_name
        # assert response_item["id"] == str(mock_response_obj.data[0].id)


    # @pytest.mark.asyncio
    # async def test_update_biomarker(self, auth_client, mock_user, mock_record, mock_user_biomarker, mock_n1_process_pipeline, monkeypatch): # Add monkeypatch
    #     """Test updating a biomarker."""
    #     update_data = {
    #         "test_name": "Updated Glucose",
    #         "result": "110",
    #         "reference_range": "70-100",
    #         "unit": "mg/dL"
    #     }

    #     # Mock the update_canonical_data method to return a schema-compliant object
    #     updated_data_mock = UserBiomarkerBase(
    #         id=mock_user_biomarker.id,
    #         record_id=mock_record.id,
    #         user_id=mock_user.id,
    #         test_name=update_data["test_name"],
    #         expanded_test_name=mock_user_biomarker.expanded_test_name,
    #         result=update_data["result"],
    #         reference_range=update_data["reference_range"],
    #         unit=update_data["unit"],
    #         context=mock_user_biomarker.context,
    #         canonical_id=mock_user_biomarker.canonical_id,
    #         edited=True,
    #         excluded=mock_user_biomarker.excluded,
    #         test_date=mock_user_biomarker.test_date,
    #         sample_source=mock_user_biomarker.sample_source,
    #         method=mock_user_biomarker.method,
    #         additional_data=mock_user_biomarker.additional_data,
    #         page_number=mock_user_biomarker.page_number,
    #         result_numeric=mock_user_biomarker.result_numeric,
    #         reference_range_min=mock_user_biomarker.reference_range_min,
    #         reference_range_max=mock_user_biomarker.reference_range_max,
    #         converted_result_numeric=mock_user_biomarker.converted_result_numeric,
    #         converted_reference_range_max=mock_user_biomarker.converted_reference_range_max,
    #         converted_reference_range_min=mock_user_biomarker.converted_reference_range_min,
    #         out_of_range=mock_user_biomarker.out_of_range,
    #         canonical_name=mock_user_biomarker.canonical_name or "Canonical Name",
    #         year=2025,
    #         month=7,
    #         day=15,
    #         filename="test.pdf",
    #         loinc_standard_name="Glucose [Mass/volume] in Blood",
    #         loinc_code="2345-7",
    #         created_at=mock_user_biomarker.created_at or datetime.now(),
    #         updated_at=datetime.now(),
    #     )
    #     # Use monkeypatch to replace the async method with an AsyncMock
    #     mock_update_canonical = AsyncMock(return_value=updated_data_mock)
    #     monkeypatch.setattr(mock_n1_process_pipeline, "update_clinical_data", mock_update_canonical)

    #     response = auth_client.put(
    #         f"/records/clinical-data/{mock_user.id}/{mock_record.id}/{mock_user_biomarker.id}",
    #         json=update_data
    #     )

    #     assert response.status_code == status.HTTP_200_OK
    #     data = response.json()
    #     assert data["test_name"] == update_data["test_name"]
    #     assert data["result"] == update_data["result"]
    #     assert data["reference_range"] == update_data["reference_range"]
    #     assert data["unit"] == update_data["unit"]
    #     # Just check that the edited field exists, don't check its value as it might vary
    #     assert "edited" in data
    #     assert data["id"] == str(mock_user_biomarker.id)

    # @pytest.mark.asyncio
    # async def test_delete_biomarker(self, auth_client, mock_user, mock_record, mock_user_biomarker, mock_n1_process_pipeline, monkeypatch): # Add monkeypatch
    #     """Test deleting a biomarker (soft delete)."""
    #     # Mock the delete_biomarker method to return a UserBiomarkerDeleted instance
    #     deleted_data_obj = UserBiomarkerDeleted(
    #         id=mock_user_biomarker.id,  # Add required id field
    #         biomarker_id=mock_user_biomarker.id, # Use UUID
    #         record_id=mock_record.id,
    #         user_id=mock_user.id,
    #         excluded=True
    #     )
    #     # Use monkeypatch to replace the async method with an AsyncMock
    #     mock_delete_biomarker = AsyncMock(return_value=deleted_data_obj)
    #     monkeypatch.setattr(mock_n1_process_pipeline, "delete_biomarker", mock_delete_biomarker)

    #     response = auth_client.delete(
    #         f"/records/clinical-data/{mock_user.id}/{mock_record.id}/{mock_user_biomarker.id}"
    #     )

    #     assert response.status_code == status.HTTP_200_OK
    #     data = response.json()
    #     # Compare response data with the mock object fields
    #     assert data["biomarker_id"] == str(deleted_data_obj.biomarker_id)
    #     assert data["record_id"] == deleted_data_obj.record_id
    #     assert data["user_id"] == str(deleted_data_obj.user_id)
    #     assert data["excluded"] is deleted_data_obj.excluded

    @pytest.mark.asyncio
    async def test_reprocess_record(self, auth_client, mock_user, mock_record, mock_n1_process_pipeline, monkeypatch):
        """Test reprocessing a single record."""
        # Mock the reprocess_single_records method
        now = datetime.now(timezone.utc)
        mock_response = {
            "record_id": mock_record.id,
            "status": "queued",
            "updated_at": now,
            "record_count": 1
        }
        mock_reprocess = AsyncMock(return_value=mock_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "reprocess_single_records", mock_reprocess)

        # Define test parameters
        parser_type = "Sequential"
        parser_cloud = "Google"

        # Make the request
        response = auth_client.post(
            f"/records/reprocess-record?user_id={mock_user.id}&record_id={mock_record.id}&parser_type={parser_type}&parser_cloud={parser_cloud}"
        )

        # Verify the response
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["record_id"] == mock_record.id
        assert data["status"] == "queued"
        assert "updated_at" in data
        assert data["record_count"] == 1

        # Verify the mocked method was called with correct arguments
        mock_reprocess.assert_called_once()
        args, _ = mock_reprocess.call_args
        assert args[0] == mock_user.id  # First arg should be user_id
        assert args[1] == mock_record.id  # Second arg should be record_id
        assert args[2] == parser_type  # Third arg should be parser_type
        assert args[3] == parser_cloud  # Fourth arg should be parser_cloud

    @pytest.mark.asyncio
    async def test_get_user_records_paginated(self, auth_client, mock_user, mock_record, mock_n1_process_pipeline, monkeypatch):
        """Test getting paginated records for a user."""
        # Create multiple mock records for pagination testing
        mock_record_1 = N1RecordUpdateRequest(
            id=mock_record.id,
            user_id=mock_user.id,
            status="COMPLETED",
            progress=100,
            url="https://example.com/test1.pdf",
            file_name="test1.pdf",
            type="RECORD",
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        mock_record_2 = N1RecordUpdateRequest(
            id=str(uuid.uuid4()),
            user_id=mock_user.id,
            status="PENDING",
            progress=50,
            url="https://example.com/test2.pdf",
            file_name="test2.pdf",
            type="RECORD",
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        # Mock the paginated response
        mock_paginated_response = PaginatedRecordResponse(
            status="success",
            user_id=mock_user.id,
            total_count=2,
            page=1,
            page_size=10,
            total_pages=1,
            data=[mock_record_1, mock_record_2]
        )

        # Mock the get_user_records_paginated method
        mock_get_paginated = AsyncMock(return_value=mock_paginated_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_records_paginated", mock_get_paginated)

        # Test basic pagination
        response = auth_client.get(f"/records/paginated?user_id={mock_user.id}&page=1&page_size=10")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Verify response structure
        assert data["status"] == "success"
        assert data["user_id"] == str(mock_user.id)
        assert data["total_count"] == 2
        assert data["page"] == 1
        assert data["page_size"] == 10
        assert data["total_pages"] == 1
        assert len(data["data"]) == 2

        # Verify record data
        assert data["data"][0]["id"] == mock_record_1.id
        assert data["data"][0]["status"] == mock_record_1.status
        assert data["data"][0]["progress"] == mock_record_1.progress
        assert data["data"][1]["id"] == mock_record_2.id
        assert data["data"][1]["status"] == mock_record_2.status
        assert data["data"][1]["progress"] == mock_record_2.progress

        # Verify the pipeline method was called
        mock_get_paginated.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_user_records_paginated_with_filters(self, auth_client, mock_user, mock_record, mock_n1_process_pipeline, monkeypatch):
        """Test getting paginated records with filters."""
        # Mock filtered response (only completed records)
        mock_record_filtered = N1RecordUpdateRequest(
            id=mock_record.id,
            user_id=mock_user.id,
            status="COMPLETED",
            progress=100,
            url="https://example.com/test.pdf",
            file_name="test.pdf",
            type="RECORD",
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        mock_paginated_response = PaginatedRecordResponse(
            status="success",
            user_id=mock_user.id,
            total_count=1,
            page=1,
            page_size=10,
            total_pages=1,
            data=[mock_record_filtered]
        )

        # Mock the get_user_records_paginated method
        mock_get_paginated = AsyncMock(return_value=mock_paginated_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_records_paginated", mock_get_paginated)

        # Test with filters
        response = auth_client.get(
            f"/records/paginated?user_id={mock_user.id}&page=1&page_size=10&status=COMPLETED&sort_by=created_at&is_descending=true"
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Verify response structure
        assert data["status"] == "success"
        assert data["user_id"] == str(mock_user.id)
        assert data["total_count"] == 1
        assert len(data["data"]) == 1

        # Verify filtered record
        assert data["data"][0]["status"] == "COMPLETED"
        assert data["data"][0]["progress"] == 100

        # Verify the pipeline method was called
        mock_get_paginated.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_user_records_paginated_empty_result(self, auth_client, mock_user, mock_n1_process_pipeline, monkeypatch):
        """Test getting paginated records with no results."""
        # Mock empty response
        mock_paginated_response = PaginatedRecordResponse(
            status="success",
            user_id=mock_user.id,
            total_count=0,
            page=1,
            page_size=10,
            total_pages=0,
            data=[]
        )

        # Mock the get_user_records_paginated method
        mock_get_paginated = AsyncMock(return_value=mock_paginated_response)
        monkeypatch.setattr(mock_n1_process_pipeline, "get_user_records_paginated", mock_get_paginated)

        # Test with filters that return no results
        response = auth_client.get(
            f"/records/paginated?user_id={mock_user.id}&page=1&page_size=10&status=NONEXISTENT"
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Verify empty response structure
        assert data["status"] == "success"
        assert data["user_id"] == str(mock_user.id)
        assert data["total_count"] == 0
        assert data["page"] == 1
        assert data["page_size"] == 10
        assert data["total_pages"] == 0
        assert len(data["data"]) == 0

        # Verify the pipeline method was called
        mock_get_paginated.assert_called_once()